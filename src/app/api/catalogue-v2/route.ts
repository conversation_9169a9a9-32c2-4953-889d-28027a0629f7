import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import {
  getAllCatalogue,
  createCatalogue,
  CatalogueValidationError,
  CatalogueDuplicateError
} from '@/services/catalogueServiceImproved';
import { validateCatalogueFilters, validatePagination } from '@/utils/catalogueValidation';
import { handleApiError } from '@/utils/errorHandling';

// Enhanced GET handler with pagination and filtering
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const clientIp = request.ip || request.headers.get('x-forwarded-for') || 'unknown';
    
    // Parse query parameters
    const filters = {
      category: searchParams.get('category') || undefined,
      minPrice: searchParams.get('minPrice') ? Number(searchParams.get('minPrice')) : undefined,
      maxPrice: searchParams.get('maxPrice') ? Number(searchParams.get('maxPrice')) : undefined,
      popular: searchParams.get('popular') ? searchParams.get('popular') === 'true' : undefined,
      search: searchParams.get('search') || undefined
    };

    const pagination = {
      page: Number(searchParams.get('page')) || 1,
      limit: Math.min(Number(searchParams.get('limit')) || 20, 100), // Cap at 100
      sortBy: (searchParams.get('sortBy') as any) || 'service',
      sortOrder: (searchParams.get('sortOrder') as any) || 'asc'
    };

    console.log('API v2: Fetching catalogue items with filters:', filters, 'pagination:', pagination);

    // Get catalogue items with enhanced service
    const result = await getAllCatalogue(filters, pagination, clientIp);

    // Enhanced response with metadata
    const response = {
      success: true,
      data: result.items,
      meta: {
        total: result.total,
        page: result.page,
        limit: result.limit,
        hasMore: result.hasMore,
        totalPages: Math.ceil(result.total / result.limit)
      },
      filters: filters,
      timestamp: new Date().toISOString()
    };

    console.log(`API v2: Retrieved ${result.items.length} of ${result.total} catalogue items`);

    // Smart caching based on content
    const cacheHeaders = result.total > 0 ? {
      'Cache-Control': 'public, max-age=300, s-maxage=600', // 5 min client, 10 min CDN
      'ETag': `"${result.total}-${result.items.length}-${Date.now()}"`,
      'Last-Modified': new Date().toUTCString()
    } : {
      'Cache-Control': 'public, max-age=60, s-maxage=120', // Shorter cache for empty results
    };

    return NextResponse.json(response, {
      status: 200,
      headers: {
        ...cacheHeaders,
        'X-Total-Count': result.total.toString(),
        'X-Page': result.page.toString(),
        'X-Has-More': result.hasMore.toString()
      }
    });

  } catch (error) {
    console.error('API v2: Error in GET /catalogue-v2:', error);

    if (error instanceof CatalogueValidationError) {
      return NextResponse.json({
        success: false,
        error: 'Validation Error',
        message: error.message,
        field: error.field,
        timestamp: new Date().toISOString()
      }, { status: 400 });
    }

    if (error instanceof Error && error.message.includes('Rate limit')) {
      return NextResponse.json({
        success: false,
        error: 'Rate Limit Exceeded',
        message: 'Too many requests. Please try again later.',
        timestamp: new Date().toISOString()
      }, { status: 429 });
    }

    return NextResponse.json({
      success: false,
      error: 'Internal Server Error',
      message: 'Failed to fetch catalogue items',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

// Enhanced POST handler for creating catalogue items
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession();
    if (!session) {
      return NextResponse.json({
        success: false,
        error: 'Unauthorized',
        message: 'Authentication required',
        timestamp: new Date().toISOString()
      }, { status: 401 });
    }

    const clientIp = request.ip || request.headers.get('x-forwarded-for') || 'unknown';
    
    // Parse request body
    let body;
    try {
      body = await request.json();
    } catch (error) {
      return NextResponse.json({
        success: false,
        error: 'Invalid JSON',
        message: 'Request body must be valid JSON',
        timestamp: new Date().toISOString()
      }, { status: 400 });
    }

    console.log('API v2: Creating catalogue item:', body);

    // Create catalogue item with enhanced service
    const newCatalogue = await createCatalogue(body, clientIp);

    const response = {
      success: true,
      data: newCatalogue,
      message: 'Catalogue item created successfully',
      timestamp: new Date().toISOString()
    };

    console.log(`API v2: Created catalogue item: ${newCatalogue.service}`);

    return NextResponse.json(response, {
      status: 201,
      headers: {
        'Cache-Control': 'no-store', // Don't cache creation responses
        'Location': `/api/catalogue-v2/${newCatalogue.id}`
      }
    });

  } catch (error) {
    console.error('API v2: Error in POST /catalogue-v2:', error);

    if (error instanceof CatalogueValidationError) {
      return NextResponse.json({
        success: false,
        error: 'Validation Error',
        message: error.message,
        field: error.field,
        timestamp: new Date().toISOString()
      }, { status: 400 });
    }

    if (error instanceof CatalogueDuplicateError) {
      return NextResponse.json({
        success: false,
        error: 'Duplicate Error',
        message: error.message,
        timestamp: new Date().toISOString()
      }, { status: 409 });
    }

    if (error instanceof Error && error.message.includes('Rate limit')) {
      return NextResponse.json({
        success: false,
        error: 'Rate Limit Exceeded',
        message: 'Too many creation requests. Please try again later.',
        timestamp: new Date().toISOString()
      }, { status: 429 });
    }

    return NextResponse.json({
      success: false,
      error: 'Internal Server Error',
      message: 'Failed to create catalogue item',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

// OPTIONS handler for CORS
export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      'Access-Control-Max-Age': '86400', // 24 hours
    },
  });
}
